import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/enums/homework/homework_status.dart';
import '../mock/mock_homeworks.dart';
import '../models/homework_model.dart';
import '../widgets/date_selector_bar.dart';
import '../widgets/homework_card.dart';

/// Screen displaying the list of homework assignments
class HomeworkListScreen extends StatefulWidget {
  const HomeworkListScreen({super.key});

  @override
  State<HomeworkListScreen> createState() => _HomeworkListScreenState();
}

class _HomeworkListScreenState extends State<HomeworkListScreen> {
  DateTime? _selectedDate; // null means 'All'
  Map<String, List<HomeworkModel>> _groupedHomework = {};

  @override
  void initState() {
    super.initState();
    _filterHomeworkByDate();
  }

  /// Filter and group homework based on selected date
  void _filterHomeworkByDate() {
    List<HomeworkModel> filteredHomework;

    if (_selectedDate == null) {
      // Show all homework
      filteredHomework = List.from(mockHomeworkList);
    } else {
      // Filter by specific date
      final selectedDay = DateTime(
        _selectedDate!.year,
        _selectedDate!.month,
        _selectedDate!.day,
      );

      filteredHomework = mockHomeworkList.where((homework) {
        // Show homework that is assigned on the selected date
        final assignedDay = DateTime(
          homework.assignedAt.year,
          homework.assignedAt.month,
          homework.assignedAt.day,
        );

        return assignedDay == selectedDay;
      }).toList();
    }

    // Sort by assigned date (most recent first)
    filteredHomework.sort((a, b) {
      return b.assignedAt.compareTo(
        a.assignedAt,
      ); // Most recently assigned first
    });

    // Group homework by date
    _groupedHomework = {};
    for (final homework in filteredHomework) {
      final dateKey = _getDateKey(homework);
      if (_groupedHomework[dateKey] == null) {
        _groupedHomework[dateKey] = [];
      }
      _groupedHomework[dateKey]!.add(homework);
    }

    // Sort homework within each group by status and assigned time
    for (final dateKey in _groupedHomework.keys) {
      _groupedHomework[dateKey]!.sort((a, b) {
        // First sort by status priority (pending/done before submitted/completed)
        final statusPriority = {
          HomeworkStatus.pending: 0,
          HomeworkStatus.done: 1,
          HomeworkStatus.submitted: 2,
          HomeworkStatus.completed: 3,
        };

        final aStatusPriority = statusPriority[a.status] ?? 0;
        final bStatusPriority = statusPriority[b.status] ?? 0;

        if (aStatusPriority != bStatusPriority) {
          return aStatusPriority.compareTo(bStatusPriority);
        }

        // Then sort by assigned time (most recent first within the same day)
        return b.assignedAt.compareTo(a.assignedAt);
      });
    }
  }

  /// Get date key for grouping homework based on assigned date
  String _getDateKey(HomeworkModel homework) {
    final assignedDate = homework.assignedAt;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final assignedDay = DateTime(
      assignedDate.year,
      assignedDate.month,
      assignedDate.day,
    );

    if (assignedDay == today) {
      return 'Today';
    } else if (assignedDay == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else if (assignedDay == today.subtract(const Duration(days: 2))) {
      return '2 days ago';
    } else if (assignedDay == today.subtract(const Duration(days: 3))) {
      return '3 days ago';
    } else if (assignedDay.isAfter(today.subtract(const Duration(days: 7)))) {
      final daysAgo = today.difference(assignedDay).inDays;
      return '$daysAgo days ago';
    } else {
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      return '${months[assignedDate.month - 1]} ${assignedDate.day}, ${assignedDate.year}';
    }
  }

  /// Handle date selection
  void _onDateSelected(DateTime? date) {
    setState(() {
      _selectedDate = date;
      _filterHomeworkByDate();
    });
  }

  /// Show date range picker
  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _selectedDate != null
          ? DateTimeRange(start: _selectedDate!, end: _selectedDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // For now, just select the start date of the range
      // In a full implementation, you might want to filter by the entire range
      setState(() {
        _selectedDate = picked.start;
        _filterHomeworkByDate();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Homework', style: theme.textTheme.titleLarge),
        actions: [
          IconButton(
            onPressed: _showDateRangePicker,
            icon: const Icon(Symbols.date_range),
            tooltip: 'Select date range',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Date selector bar
            DateSelectorBar(
              selectedDate: _selectedDate,
              onDateSelected: _onDateSelected,
            ),

            // Homework list
            Expanded(
              child: _groupedHomework.isEmpty
                  ? _EmptyState(selectedDate: _selectedDate)
                  : _GroupedHomeworkList(groupedHomework: _groupedHomework),
            ),
          ],
        ),
      ),
    );
  }
}

/// Grouped homework list widget
class _GroupedHomeworkList extends StatelessWidget {
  final Map<String, List<HomeworkModel>> groupedHomework;

  const _GroupedHomeworkList({required this.groupedHomework});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final sortedKeys = groupedHomework.keys.toList()..sort(_sortDateKeys);

    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      itemCount: sortedKeys.length,
      itemBuilder: (context, index) {
        final dateKey = sortedKeys[index];
        final homeworkList = groupedHomework[dateKey]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date header
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              child: Text(
                dateKey,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
            // Homework cards for this date
            ...homeworkList.map((homework) => HomeworkCard(homework: homework)),
            SizedBox(height: 8.h),
          ],
        );
      },
    );
  }

  /// Sort date keys to show most recent assignments first
  int _sortDateKeys(String a, String b) {
    // Priority order: Today, Yesterday, 2 days ago, 3 days ago, etc.
    const priorityOrder = [
      'Today',
      'Yesterday',
      '2 days ago',
      '3 days ago',
      '4 days ago',
      '5 days ago',
      '6 days ago',
      '7 days ago',
    ];

    final aIndex = priorityOrder.indexOf(a);
    final bIndex = priorityOrder.indexOf(b);

    // Both are priority dates
    if (aIndex != -1 && bIndex != -1) {
      return aIndex.compareTo(bIndex);
    }

    // Only a is priority
    if (aIndex != -1) {
      return -1;
    }

    // Only b is priority
    if (bIndex != -1) {
      return 1;
    }

    // Both are regular dates - sort by date (most recent first)
    // Parse dates for proper comparison
    try {
      final aDate = _parseDateKey(a);
      final bDate = _parseDateKey(b);
      return bDate.compareTo(aDate); // Most recent first
    } catch (e) {
      // Fallback to alphabetical if parsing fails
      return a.compareTo(b);
    }
  }

  /// Parse date key back to DateTime for sorting
  DateTime _parseDateKey(String dateKey) {
    // Format: "Jan 15, 2024"
    final parts = dateKey.split(' ');
    if (parts.length != 3) throw FormatException('Invalid date format');

    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    final monthIndex = months.indexOf(parts[0]) + 1;
    final day = int.parse(parts[1].replaceAll(',', ''));
    final year = int.parse(parts[2]);

    return DateTime(year, monthIndex, day);
  }
}

/// Empty state widget when no homework is found
class _EmptyState extends StatelessWidget {
  final DateTime? selectedDate;

  const _EmptyState({required this.selectedDate});

  String _formatDate(DateTime? date) {
    if (date == null) return 'all dates';

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(date.year, date.month, date.day);

    if (selectedDay == today) {
      return 'today';
    } else if (selectedDay == today.add(const Duration(days: 1))) {
      return 'tomorrow';
    } else if (selectedDay == today.subtract(const Duration(days: 1))) {
      return 'yesterday';
    } else {
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      return '${months[date.month - 1]} ${date.day}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Symbols.assignment,
              size: 64.sp,
              color: isDark
                  ? AppColors.textSecondaryDark
                  : AppColors.textSecondaryLight,
            ),
            SizedBox(height: 16.h),
            Text(
              'No homework for ${_formatDate(selectedDate)}',
              style: theme.textTheme.titleMedium?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'Enjoy your free time!',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
