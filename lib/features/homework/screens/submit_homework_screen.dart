import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../core/enums/homework/submission_type.dart';
import '../../../core/services/file_picker_service.dart';
import '../models/homework_model.dart';
import '../models/uploaded_file_model.dart';
import '../mock/mock_homeworks.dart';

/// Screen for submitting homework assignments
class SubmitHomeworkScreen extends StatefulWidget {
  /// The homework to submit (can be passed via navigation arguments)
  final HomeworkModel? homework;

  const SubmitHomeworkScreen({super.key, this.homework});

  @override
  State<SubmitHomeworkScreen> createState() => _SubmitHomeworkScreenState();
}

class _SubmitHomeworkScreenState extends State<SubmitHomeworkScreen> {
  late final HomeworkModel _homework;
  final TextEditingController _noteController = TextEditingController();
  final List<UploadedFileModel> _uploadedFiles = [];

  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    // Use provided homework or fallback to mock data
    _homework = widget.homework ?? mockHomeworkList.first;

    // Pre-fill with existing submission data if editing
    _loadExistingSubmission();
  }

  /// Load existing submission data if this is an edit operation
  void _loadExistingSubmission() {
    try {
      final existingSubmission = mockHomeworkSubmissions.firstWhere(
        (submission) => submission.homeworkId == _homework.id,
      );

      // Pre-fill the note
      _noteController.text = existingSubmission.studentNote ?? '';

      // Convert file URLs to UploadedFileModel for display
      // Note: In a real app, you'd need to handle this differently
      // as we can't recreate File objects from URLs
      final uploadedFiles = existingSubmission.fileUrls.map((url) {
        final fileName = url.split('/').last;
        return UploadedFileModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          file: File(
            url,
          ), // This is just for display, won't work for actual file operations
          fileName: fileName,
          fileSize: 'Unknown size',
          fileExtension: fileName.split('.').last.toLowerCase(),
          uploadedAt: existingSubmission.submittedAt,
          fileType: _getFileTypeFromExtension(
            fileName.split('.').last.toLowerCase(),
          ),
        );
      }).toList();

      setState(() {
        _uploadedFiles.addAll(uploadedFiles);
      });

      debugPrint(
        'Loaded existing submission for editing: ${existingSubmission.homeworkId}',
      );
    } catch (e) {
      // No existing submission found, this is a new submission
      debugPrint('No existing submission found, creating new submission');
    }
  }

  /// Get file type from extension
  FileType _getFileTypeFromExtension(String extension) {
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
      return FileType.image;
    } else if ([
      'mp4',
      'mov',
      'avi',
      'mkv',
      'wmv',
      'flv',
      '3gp',
    ].contains(extension)) {
      return FileType.video;
    } else if ([
      'pdf',
      'doc',
      'docx',
      'txt',
      'rtf',
      'odt',
    ].contains(extension)) {
      return FileType.document;
    } else {
      return FileType.other;
    }
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  /// Check if we're editing an existing submission
  bool _isEditingExistingSubmission() {
    try {
      mockHomeworkSubmissions.firstWhere(
        (submission) => submission.homeworkId == _homework.id,
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Show file upload options bottom sheet
  void _showFileUploadOptions() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (context) => _FileUploadOptionsSheet(
        onFilesSelected: _addFiles,
        onCameraCapture: _captureFromCamera,
        isUploading: _isUploading,
      ),
    );
  }

  /// Add files to the uploaded files list
  void _addFiles(List<File> files) async {
    setState(() {
      _isUploading = true;
    });

    try {
      final uploadedFiles = files
          .map((file) => UploadedFileModel.fromFile(file))
          .toList();

      setState(() {
        _uploadedFiles.addAll(uploadedFiles);
        _isUploading = false;
      });

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${files.length} file(s) added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isUploading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding files: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Capture photo/video from camera
  void _captureFromCamera(File file) async {
    try {
      final uploadedFile = UploadedFileModel.fromFile(file);

      setState(() {
        _uploadedFiles.add(uploadedFile);
      });

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('File captured successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error capturing file: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Remove a file from the uploaded files list
  void _removeFile(int index) {
    setState(() {
      _uploadedFiles.removeAt(index);
    });
  }

  /// Submit the homework
  void _submitHomework() {
    // Simulate submission
    debugPrint('Submitting homework: ${_homework.title}');
    debugPrint('Files: $_uploadedFiles');
    debugPrint('Note: ${_noteController.text}');

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Homework submitted successfully!'),
        backgroundColor: Colors.green,
      ),
    );

    // Navigate back after submission
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditingExistingSubmission()
              ? 'Edit Submission'
              : 'Submit Homework',
          style: theme.textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _HomeworkInfoSection(homework: _homework),

                  SizedBox(height: 24.h),

                  _FileUploadSection(
                    uploadedFiles: _uploadedFiles,
                    onRemoveFile: _removeFile,
                  ),
                  SizedBox(height: 16.h),
                  _NoteInputSection(controller: _noteController),
                  SizedBox(height: 24.h),
                  _SubmitButton(
                    onSubmit: _submitHomework,
                    isEnabled: true,
                    buttonText: _isEditingExistingSubmission()
                        ? 'Update Submission'
                        : 'Submit',
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showFileUploadOptions,
        backgroundColor: colorScheme.primary,
        child: Icon(Symbols.add, color: colorScheme.onPrimary),
      ),
    );
  }
}

class _HomeworkInfoSection extends StatelessWidget {
  final HomeworkModel homework;

  const _HomeworkInfoSection({required this.homework});

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'No due date';

    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateDay = DateTime(dateTime.year, dateTime.month, dateTime.day);

    String dateStr;
    if (dateDay == today) {
      dateStr = 'Today';
    } else if (dateDay == today.add(const Duration(days: 1))) {
      dateStr = 'Tomorrow';
    } else if (dateDay == today.subtract(const Duration(days: 1))) {
      dateStr = 'Yesterday';
    } else {
      dateStr =
          '${months[dateTime.month - 1]} ${dateTime.day}, ${dateTime.year}';
    }

    final hour = dateTime.hour == 0
        ? 12
        : (dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour);
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';

    return '$dateStr at $hour:$minute $period';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Homework Details',
          style: theme.textTheme.titleMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        _InfoRow(label: 'Subject', value: homework.subject),
        SizedBox(height: 12.h),
        _InfoRow(label: 'Title', value: homework.title),
        SizedBox(height: 12.h),
        _InfoRow(label: 'Due Date', value: _formatDateTime(homework.dueAt)),
        SizedBox(height: 12.h),
        _InfoRow(
          label: 'Submission Type',
          value: homework.submissionType.label,
        ),
      ],
    );
  }
}

class _InfoRow extends StatelessWidget {
  final String label;
  final String value;

  const _InfoRow({required this.label, required this.value});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100.w,
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
            ),
          ),
        ),
      ],
    );
  }
}

class _FileUploadSection extends StatelessWidget {
  final List<UploadedFileModel> uploadedFiles;
  final Function(int) onRemoveFile;

  const _FileUploadSection({
    required this.uploadedFiles,
    required this.onRemoveFile,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Uploaded Files',
          style: theme.textTheme.titleMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        if (uploadedFiles.isEmpty)
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(24.w),
            decoration: BoxDecoration(
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.3),
                style: BorderStyle.solid,
              ),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              children: [
                Icon(
                  Symbols.upload_file,
                  size: 48.sp,
                  color: colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                SizedBox(height: 12.h),
                Text(
                  'No files uploaded yet',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Tap the + button to add files',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          )
        else
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: uploadedFiles.length,
            separatorBuilder: (context, index) => SizedBox(height: 8.h),
            itemBuilder: (context, index) => SubmissionFileTile(
              uploadedFile: uploadedFiles[index],
              onRemove: () => onRemoveFile(index),
            ),
          ),
      ],
    );
  }
}

class SubmissionFileTile extends StatelessWidget {
  final UploadedFileModel uploadedFile;
  final VoidCallback onRemove;

  const SubmissionFileTile({
    super.key,
    required this.uploadedFile,
    required this.onRemove,
  });

  IconData _getFileIcon(String extension) {
    switch (extension) {
      case 'pdf':
        return Symbols.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Symbols.description;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Symbols.image;
      case 'mp4':
      case 'mov':
      case 'avi':
        return Symbols.videocam;
      default:
        return Symbols.attach_file;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final extension = uploadedFile.fileExtension;

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Icon(
            _getFileIcon(extension),
            size: 24.sp,
            color: colorScheme.primary,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  uploadedFile.fileName,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 2.h),
                Text(
                  uploadedFile.fileSize,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: onRemove,
            icon: Icon(Symbols.close, size: 20.sp, color: colorScheme.error),
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(minWidth: 32.w, minHeight: 32.h),
          ),
        ],
      ),
    );
  }
}

class _NoteInputSection extends StatelessWidget {
  final TextEditingController controller;

  const _NoteInputSection({required this.controller});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Submission Note (Optional)',
          style: theme.textTheme.titleMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        TextField(
          controller: controller,
          maxLines: 4,
          decoration: InputDecoration(
            hintText: 'Add any notes or comments about your submission...',
            hintStyle: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(
                color: colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(
                color: colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(color: colorScheme.primary, width: 2),
            ),
            contentPadding: EdgeInsets.all(16.w),
          ),
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}

class _SubmitButton extends StatelessWidget {
  final VoidCallback onSubmit;
  final bool isEnabled;
  final String buttonText;

  const _SubmitButton({
    required this.onSubmit,
    required this.isEnabled,
    this.buttonText = 'Submit',
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      child: ElevatedButton(
        onPressed: isEnabled ? onSubmit : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          disabledBackgroundColor: colorScheme.onSurface.withValues(
            alpha: 0.12,
          ),
          disabledForegroundColor: colorScheme.onSurface.withValues(
            alpha: 0.38,
          ),
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          elevation: 0,
        ),
        child: Text(
          buttonText,
          style: theme.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}

class _FileUploadOptionsSheet extends StatelessWidget {
  final Function(List<File>) onFilesSelected;
  final Function(File) onCameraCapture;
  final bool isUploading;

  const _FileUploadOptionsSheet({
    required this.onFilesSelected,
    required this.onCameraCapture,
    required this.isUploading,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(24.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          SizedBox(height: 24.h),

          // Upload from Files option
          _UploadOption(
            icon: Symbols.folder_open,
            title: 'Upload from Files',
            subtitle: 'Choose files from your device',
            isLoading: isUploading,
            onTap: isUploading
                ? null
                : () {
                    FilePickerService().pickFiles().then((files) {
                      if (files != null && files.isNotEmpty) {
                        onFilesSelected(files);
                      }
                    });
                  },
          ),

          SizedBox(height: 16.h),

          // Upload Images option
          _UploadOption(
            icon: Symbols.image,
            title: 'Upload Images',
            subtitle: 'Choose from gallery',
            isLoading: isUploading,
            onTap: isUploading
                ? null
                : () {
                    FilePickerService().pickImages().then((files) {
                      if (files != null && files.isNotEmpty) {
                        onFilesSelected(files);
                      }
                    });
                  },
          ),

          SizedBox(height: 16.h),

          // Capture Photo option
          _UploadOption(
            icon: Symbols.photo_camera,
            title: 'Capture Photo',
            subtitle: 'Take a photo with camera',
            isLoading: isUploading,
            onTap: isUploading
                ? null
                : () {
                    FilePickerService().capturePhoto().then((file) {
                      if (file != null) {
                        onCameraCapture(file);
                      }
                    });
                  },
          ),

          SizedBox(height: 24.h),
        ],
      ),
    );
  }
}

class _UploadOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback? onTap;
  final bool isLoading;

  const _UploadOption({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDisabled = onTap == null || isLoading;

    return InkWell(
      onTap: isDisabled ? null : onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          border: Border.all(
            color: isDisabled
                ? colorScheme.outline.withValues(alpha: 0.1)
                : colorScheme.outline.withValues(alpha: 0.2),
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: isDisabled
                    ? colorScheme.onSurface.withValues(alpha: 0.1)
                    : colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: isLoading
                  ? SizedBox(
                      width: 24.sp,
                      height: 24.sp,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          colorScheme.primary,
                        ),
                      ),
                    )
                  : Icon(
                      icon,
                      size: 24.sp,
                      color: isDisabled
                          ? colorScheme.onSurface.withValues(alpha: 0.4)
                          : colorScheme.primary,
                    ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: isDisabled
                          ? colorScheme.onSurface.withValues(alpha: 0.4)
                          : colorScheme.onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isDisabled
                          ? colorScheme.onSurface.withValues(alpha: 0.3)
                          : colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
            if (!isLoading)
              Icon(
                Symbols.chevron_right,
                size: 20.sp,
                color: isDisabled
                    ? colorScheme.onSurface.withValues(alpha: 0.3)
                    : colorScheme.onSurface.withValues(alpha: 0.5),
              ),
          ],
        ),
      ),
    );
  }
}
