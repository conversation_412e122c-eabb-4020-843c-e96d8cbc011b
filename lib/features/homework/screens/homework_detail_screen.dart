import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:scholara_student/core/routes/app_routes.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/enums/homework/homework_status.dart';
import '../../../core/enums/homework/submission_type.dart';
import '../mock/mock_homeworks.dart';
import '../models/homework_model.dart';
import '../widgets/general_info_section.dart';
import '../widgets/resource_list.dart';

/// Screen displaying detailed information about a homework assignment
class HomeworkDetailScreen extends StatefulWidget {
  /// The ID of the homework to display
  final String homeworkId;

  const HomeworkDetailScreen({super.key, required this.homeworkId});

  @override
  State<HomeworkDetailScreen> createState() => _HomeworkDetailScreenState();
}

class _HomeworkDetailScreenState extends State<HomeworkDetailScreen> {
  late HomeworkModel homework;
  String? personalNotes;

  @override
  void initState() {
    super.initState();
    _loadHomework();
  }

  /// Load homework data from mock data
  void _loadHomework() {
    homework = mockHomeworkList.firstWhere(
      (hw) => hw.id == widget.homeworkId,
      orElse: () => mockHomeworkList.first, // Fallback to first homework
    );
  }

  /// Show three-dot menu options
  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Symbols.share),
              title: const Text('Share'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement share functionality
              },
            ),
            ListTile(
              leading: const Icon(Symbols.flag),
              title: const Text('Report Issue'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement report functionality
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Show personal notes editor modal
  void _showNotesEditor() {
    final controller = TextEditingController(text: personalNotes ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Your Notes'),
        content: SizedBox(
          width: double.maxFinite,
          child: TextField(
            controller: controller,
            maxLines: 8,
            decoration: const InputDecoration(
              hintText: 'Add your personal notes here...',
              border: OutlineInputBorder(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                personalNotes = controller.text.trim().isEmpty
                    ? null
                    : controller.text.trim();
              });
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  /// Handle action button press
  void _handleActionButton() {
    if (homework.requiresSubmission &&
        homework.submissionType == SubmissionType.online) {
      // Online submission logic
      switch (homework.status) {
        case HomeworkStatus.pending:
          // Navigate to submit screen
          context.pushNamed(
            RouteNames.submitHomework,
            pathParameters: {'id': homework.id},
          );
          break;
        case HomeworkStatus.submitted:
        case HomeworkStatus.accepted:
        case HomeworkStatus.rejected:
          // Navigate to view submission screen
          context.pushNamed(
            RouteNames.viewSubmission,
            pathParameters: {'id': homework.id},
          );
          break;
        case HomeworkStatus.done:
          // Shouldn't happen for online submissions, but handle gracefully
          context.pushNamed(
            RouteNames.submitHomework,
            pathParameters: {'id': homework.id},
          );
          break;
      }
    } else {
      // Offline submission or no submission required
      switch (homework.status) {
        case HomeworkStatus.pending:
          // Mark as done
          setState(() {
            homework = homework.copyWith(status: HomeworkStatus.done);
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Homework marked as done!')),
          );
          break;
        case HomeworkStatus.done:
          // Mark as undone (back to pending)
          setState(() {
            homework = homework.copyWith(status: HomeworkStatus.pending);
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Homework marked as undone!')),
          );
          break;
        case HomeworkStatus.submitted:
        case HomeworkStatus.accepted:
        case HomeworkStatus.rejected:
          // Navigate to view submission screen for offline submissions
          context.pushNamed(
            RouteNames.viewSubmission,
            pathParameters: {'id': homework.id},
          );
          break;
      }
    }
  }

  /// Get action button text based on homework state
  String _getActionButtonText() {
    if (homework.requiresSubmission &&
        homework.submissionType == SubmissionType.online) {
      // Online submission logic
      switch (homework.status) {
        case HomeworkStatus.pending:
          return 'Submit';
        case HomeworkStatus.submitted:
        case HomeworkStatus.accepted:
        case HomeworkStatus.rejected:
          return 'View Submission';
        case HomeworkStatus.done:
          return 'Submit'; // Shouldn't happen for online submissions
      }
    } else {
      // Offline submission or no submission required
      switch (homework.status) {
        case HomeworkStatus.pending:
          return 'Mark as Done';
        case HomeworkStatus.done:
          return 'Mark as Undone';
        case HomeworkStatus.submitted:
        case HomeworkStatus.accepted:
        case HomeworkStatus.rejected:
          return 'View Submission'; // For offline submissions that were reviewed
      }
    }
  }

  /// Get action button icon based on homework state
  Icon _getActionButtonIcon() {
    if (homework.requiresSubmission &&
        homework.submissionType == SubmissionType.online) {
      // Online submission logic
      switch (homework.status) {
        case HomeworkStatus.pending:
          return const Icon(Symbols.upload);
        case HomeworkStatus.submitted:
        case HomeworkStatus.accepted:
        case HomeworkStatus.rejected:
          return const Icon(Symbols.visibility);
        case HomeworkStatus.done:
          return const Icon(
            Symbols.upload,
          ); // Shouldn't happen for online submissions
      }
    } else {
      // Offline submission or no submission required
      switch (homework.status) {
        case HomeworkStatus.pending:
          return const Icon(Symbols.check);
        case HomeworkStatus.done:
          return const Icon(Symbols.undo);
        case HomeworkStatus.submitted:
        case HomeworkStatus.accepted:
        case HomeworkStatus.rejected:
          return const Icon(
            Symbols.visibility,
          ); // For offline submissions that were reviewed
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      // backgroundColor: theme.colorScheme.onPrimary,
      appBar: AppBar(
        title: Text(homework.subject, style: theme.textTheme.titleLarge),
        actions: [
          IconButton(
            onPressed: _showMoreOptions,
            icon: const Icon(Symbols.more_vert),
            tooltip: 'More options',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.all(16.w),

              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GeneralInfoSection(homework: homework),

                  SizedBox(height: 24.h),

                  // Resources Section
                  if (homework.resourceUrls.isNotEmpty) ...[
                    ResourceList(resourceUrls: homework.resourceUrls),
                    SizedBox(height: 24.h),
                  ],

                  // Teacher Notes Section
                  if (homework.teacherNote != null) ...[
                    _TeacherNotesSection(note: homework.teacherNote!),
                    SizedBox(height: 24.h),
                  ],

                  // Personal Notes Section
                  _PersonalNotesSection(
                    notes: personalNotes,
                    onEditPressed: _showNotesEditor,
                  ),

                  // Add bottom padding to account for FAB
                  SizedBox(height: 80.h),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _handleActionButton,
        icon: _getActionButtonIcon(),
        label: Text(
          _getActionButtonText(),
          style: theme.textTheme.titleSmall?.copyWith(
            color: theme.colorScheme.onPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}

/// Teacher notes section widget
class _TeacherNotesSection extends StatelessWidget {
  final String note;

  const _TeacherNotesSection({required this.note});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Teacher Notes',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(note, style: theme.textTheme.bodyMedium),
        ],
      ),
    );
  }
}

/// Personal notes section widget
class _PersonalNotesSection extends StatelessWidget {
  final String? notes;
  final VoidCallback onEditPressed;

  const _PersonalNotesSection({
    required this.notes,
    required this.onEditPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Your Notes',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: onEditPressed,
                child: Text(notes == null ? 'Add' : 'Edit'),
              ),
            ],
          ),
          if (notes != null) ...[
            SizedBox(height: 8.h),
            Text(notes!, style: theme.textTheme.bodyMedium),
          ] else ...[
            SizedBox(height: 8.h),
            Text(
              'No personal notes added yet.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark
                    ? AppColors.textSecondaryDark
                    : AppColors.textSecondaryLight,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
