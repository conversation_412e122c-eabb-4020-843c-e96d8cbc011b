import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/enums/homework/homework_status.dart';
import '../../../core/enums/homework/submission_type.dart';
import '../models/homework_model.dart';

/// Card widget to display homework information
class HomeworkCard extends StatelessWidget {
  /// The homework model to display
  final HomeworkModel homework;

  const HomeworkCard({super.key, required this.homework});

  /// Format due time for display
  String _formatDueTime(DateTime? dueAt) {
    if (dueAt == null) return 'No due date';

    final now = DateTime.now();
    final difference = dueAt.difference(now);

    if (difference.isNegative) {
      return 'Overdue';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} left';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} left';
    } else {
      return 'Due soon';
    }
  }

  /// Get color for due time based on urgency
  Color _getDueTimeColor(DateTime? dueAt, bool isDark) {
    if (dueAt == null) {
      return isDark
          ? AppColors.textSecondaryDark
          : AppColors.textSecondaryLight;
    }

    final now = DateTime.now();
    final difference = dueAt.difference(now);

    if (difference.isNegative) {
      return isDark ? AppColors.errorDark : AppColors.errorLight;
    } else if (difference.inDays <= 1) {
      return isDark ? AppColors.warningDark : AppColors.warningLight;
    } else {
      return isDark
          ? AppColors.textSecondaryDark
          : AppColors.textSecondaryLight;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: () {
          context.pushNamed(
            RouteNames.homeworkDetail,
            pathParameters: {'id': homework.id},
          );
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with subject and submission type
              Row(
                children: [
                  // Subject chip
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Text(
                      homework.subject,
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const Spacer(),
                  // Submission type icon
                  Icon(
                    homework.submissionType.requiresFileUpload
                        ? Symbols.cloud_upload
                        : Symbols.assignment_turned_in,
                    size: 20.sp,
                    color: isDark
                        ? AppColors.textSecondaryDark
                        : AppColors.textSecondaryLight,
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Title
              Text(
                homework.title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              // Description (if available)
              if (homework.description != null) ...[
                SizedBox(height: 8.h),
                Text(
                  homework.description!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isDark
                        ? AppColors.textSecondaryDark
                        : AppColors.textSecondaryLight,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              SizedBox(height: 12.h),

              // Bottom row with due time and status
              Row(
                children: [
                  // Due time
                  Row(
                    children: [
                      Icon(
                        Symbols.schedule,
                        size: 16.sp,
                        color: _getDueTimeColor(homework.dueAt, isDark),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        _formatDueTime(homework.dueAt),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: _getDueTimeColor(homework.dueAt, isDark),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),

                  const Spacer(),

                  // Status chip
                  _StatusChip(status: homework.status),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Status chip widget to display homework status
class _StatusChip extends StatelessWidget {
  final HomeworkStatus status;

  const _StatusChip({required this.status});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: status.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(
          color: status.color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        status.label,
        style: theme.textTheme.labelSmall?.copyWith(
          color: status.color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
