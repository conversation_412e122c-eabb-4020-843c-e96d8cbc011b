import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/theme/app_colors.dart';

/// Widget displaying a list of homework resources
class ResourceList extends StatelessWidget {
  /// List of resource URLs
  final List<String> resourceUrls;

  const ResourceList({
    super.key,
    required this.resourceUrls,
  });

  /// Extract file name from URL
  String _getFileName(String url) {
    try {
      final uri = Uri.parse(url);
      final segments = uri.pathSegments;
      if (segments.isNotEmpty) {
        return segments.last;
      }
      return uri.host;
    } catch (e) {
      return 'Resource';
    }
  }

  /// Get file extension from URL
  String? _getFileExtension(String url) {
    try {
      final fileName = _getFileName(url);
      final lastDot = fileName.lastIndexOf('.');
      if (lastDot != -1 && lastDot < fileName.length - 1) {
        return fileName.substring(lastDot + 1).toLowerCase();
      }
    } catch (e) {
      // Ignore error
    }
    return null;
  }

  /// Get appropriate icon for file type
  IconData _getFileIcon(String url) {
    final extension = _getFileExtension(url);
    
    switch (extension) {
      case 'pdf':
        return Symbols.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Symbols.description;
      case 'xls':
      case 'xlsx':
        return Symbols.table_chart;
      case 'ppt':
      case 'pptx':
        return Symbols.slideshow;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Symbols.image;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Symbols.video_file;
      case 'mp3':
      case 'wav':
        return Symbols.audio_file;
      case 'zip':
      case 'rar':
        return Symbols.folder_zip;
      case 'txt':
        return Symbols.text_snippet;
      case 'py':
      case 'java':
      case 'cpp':
      case 'js':
      case 'html':
      case 'css':
        return Symbols.code;
      default:
        return Symbols.link;
    }
  }

  /// Handle resource tap
  Future<void> _handleResourceTap(BuildContext context, String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Cannot open: ${_getFileName(url)}')),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error opening: ${_getFileName(url)}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Resources',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          ...resourceUrls.asMap().entries.map((entry) {
            final index = entry.key;
            final url = entry.value;
            
            return Column(
              children: [
                if (index > 0) SizedBox(height: 8.h),
                _ResourceItem(
                  url: url,
                  fileName: _getFileName(url),
                  icon: _getFileIcon(url),
                  onTap: () => _handleResourceTap(context, url),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }
}

/// Individual resource item widget
class _ResourceItem extends StatelessWidget {
  final String url;
  final String fileName;
  final IconData icon;
  final VoidCallback onTap;

  const _ResourceItem({
    required this.url,
    required this.fileName,
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: (isDark ? AppColors.borderDark : AppColors.borderLight).withValues(alpha: 0.5),
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 24.sp,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    fileName,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (url.length > 50) ...[
                    SizedBox(height: 2.h),
                    Text(
                      url,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            SizedBox(width: 8.w),
            Icon(
              Symbols.open_in_new,
              size: 16.sp,
              color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
            ),
          ],
        ),
      ),
    );
  }
}
