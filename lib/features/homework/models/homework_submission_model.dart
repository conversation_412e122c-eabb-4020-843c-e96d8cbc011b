/// Model representing a homework submission by a student
class HomeworkSubmissionModel {

  final String homeworkId;
  final String userId;
  final List<String> fileUrls;
  final String? studentNote;
  final DateTime submittedAt;

  const HomeworkSubmissionModel({
    required this.homeworkId,
    required this.userId,
    required this.fileUrls,
    this.studentNote,
    required this.submittedAt,
  });

  factory HomeworkSubmissionModel.fromJson(Map<String, dynamic> json) {
    return HomeworkSubmissionModel(
      homeworkId: json['homeworkId'] as String,
      userId: json['userId'] as String,
      fileUrls: List<String>.from(json['fileUrls'] as List),
      studentNote: json['studentNote'] as String?,
      submittedAt: DateTime.parse(json['submittedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'homeworkId': homeworkId,
      'userId': userId,
      'fileUrls': fileUrls,
      'studentNote': studentNote,
      'submittedAt': submittedAt.toIso8601String(),
    };
  }

  HomeworkSubmissionModel copyWith({
    String? homeworkId,
    String? userId,
    List<String>? fileUrls,
    String? studentNote,
    DateTime? submittedAt,
  }) {
    return HomeworkSubmissionModel(
      homeworkId: homeworkId ?? this.homeworkId,
      userId: userId ?? this.userId,
      fileUrls: fileUrls ?? this.fileUrls,
      studentNote: studentNote ?? this.studentNote,
      submittedAt: submittedAt ?? this.submittedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! HomeworkSubmissionModel) return false;
    return other.homeworkId == homeworkId &&
        other.userId == userId &&
        _listEquals(other.fileUrls, fileUrls) &&
        other.studentNote == studentNote &&
        other.submittedAt == submittedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      homeworkId,
      userId,
      Object.hashAll(fileUrls),
      studentNote,
      submittedAt,
    );
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'HomeworkSubmissionModel(homeworkId: $homeworkId, userId: $userId, submittedAt: $submittedAt)';
  }
}
