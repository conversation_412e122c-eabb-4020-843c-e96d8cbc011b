import '../../../core/enums/homework/homework_status.dart';
import '../../../core/enums/homework/submission_type.dart';

class HomeworkModel {
  final String id;
  final String subject;
  final String title;
  final String? description;
  final DateTime assignedAt;
  final DateTime? dueAt;
  final bool requiresSubmission;
  final SubmissionType submissionType;
  final HomeworkStatus status;
  final List<String> resourceUrls;
  final String? teacherNote;
  final String? classId;
  final String? teacherId;
  final String? submissionId;

  const HomeworkModel({
    required this.id,
    required this.subject,
    required this.title,
    this.description,
    required this.assignedAt,
    this.dueAt,
    required this.requiresSubmission,
    required this.submissionType,
    required this.status,
    required this.resourceUrls,
    this.teacherNote,
    this.classId,
    this.teacherId,
    this.submissionId,
  });

  factory HomeworkModel.fromJson(Map<String, dynamic> json) {
    return HomeworkModel(
      id: json['id'] as String,
      subject: json['subject'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      assignedAt: DateTime.parse(json['assignedAt'] as String),
      dueAt: json['dueAt'] != null
          ? DateTime.parse(json['dueAt'] as String)
          : null,
      requiresSubmission: json['requiresSubmission'] as bool,
      submissionType: SubmissionType.values.firstWhere(
        (e) => e.name == json['submissionType'],
      ),
      status: HomeworkStatus.values.firstWhere((e) => e.name == json['status']),
      resourceUrls: List<String>.from(json['resourceUrls'] as List),
      teacherNote: json['teacherNote'] as String?,
      classId: json['classId'] as String?,
      teacherId: json['teacherId'] as String?,
      submissionId: json['submissionId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'subject': subject,
      'title': title,
      'description': description,
      'assignedAt': assignedAt.toIso8601String(),
      'dueAt': dueAt?.toIso8601String(),
      'requiresSubmission': requiresSubmission,
      'submissionType': submissionType.name,
      'status': status.name,
      'resourceUrls': resourceUrls,
      'teacherNote': teacherNote,
      'classId': classId,
      'teacherId': teacherId,
      'submissionId': submissionId,
    };
  }

  HomeworkModel copyWith({
    String? id,
    String? subject,
    String? title,
    String? description,
    DateTime? assignedAt,
    DateTime? dueAt,
    bool? requiresSubmission,
    SubmissionType? submissionType,
    HomeworkStatus? status,
    List<String>? resourceUrls,
    String? teacherNote,
    String? classId,
    String? teacherId,
    String? submissionId,
  }) {
    return HomeworkModel(
      id: id ?? this.id,
      subject: subject ?? this.subject,
      title: title ?? this.title,
      description: description ?? this.description,
      assignedAt: assignedAt ?? this.assignedAt,
      dueAt: dueAt ?? this.dueAt,
      requiresSubmission: requiresSubmission ?? this.requiresSubmission,
      submissionType: submissionType ?? this.submissionType,
      status: status ?? this.status,
      resourceUrls: resourceUrls ?? this.resourceUrls,
      teacherNote: teacherNote ?? this.teacherNote,
      classId: classId ?? this.classId,
      teacherId: teacherId ?? this.teacherId,
      submissionId: submissionId ?? this.submissionId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! HomeworkModel) return false;

    return other.id == id &&
        other.subject == subject &&
        other.title == title &&
        other.description == description &&
        other.assignedAt == assignedAt &&
        other.dueAt == dueAt &&
        other.requiresSubmission == requiresSubmission &&
        other.submissionType == submissionType &&
        other.status == status &&
        _listEquals(other.resourceUrls, resourceUrls) &&
        other.teacherNote == teacherNote &&
        other.classId == classId &&
        other.teacherId == teacherId &&
        other.submissionId == submissionId;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      subject,
      title,
      description,
      assignedAt,
      dueAt,
      requiresSubmission,
      submissionType,
      status,
      Object.hashAll(resourceUrls),
      teacherNote,
      classId,
      teacherId,
      submissionId,
    );
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'HomeworkModel(id: $id, subject: $subject, title: $title, status: $status)';
  }
}
