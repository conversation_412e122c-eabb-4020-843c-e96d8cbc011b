import 'package:faker/faker.dart';
import '../../../core/enums/homework/homework_status.dart';
import '../../../core/enums/homework/submission_type.dart';
import '../models/homework_model.dart';
import '../models/homework_submission_model.dart';

/// Mock data for homework assignments
final List<HomeworkModel> mockHomeworkList = [
  // Mathematics homework
  HomeworkModel(
    id: 'hw_001',
    subject: 'Mathematics',
    title: 'Algebra Practice Problems',
    description:
        'Complete exercises 1-20 from chapter 5. Show all working steps.',
    assignedAt: DateTime.now(),
    dueAt: DateTime.now().add(const Duration(days: 2)),
    requiresSubmission: true,
    submissionType: SubmissionType.online,
    status: HomeworkStatus.pending,
    resourceUrls: ['https://example.com/algebra_worksheet.pdf'],
    teacherNote: 'Focus on quadratic equations',
    classId: 'class_math_10a',
    teacherId: 'teacher_001',
  ),

  HomeworkModel(
    id: 'hw_002',
    subject: 'Mathematics',
    title: 'Geometry Proofs',
    description: 'Prove the given theorems using logical reasoning.',
    assignedAt: DateTime.now().subtract(const Duration(days: 5)),
    dueAt: DateTime.now().subtract(const Duration(days: 1)),
    requiresSubmission: true,
    submissionType: SubmissionType.online,
    status: HomeworkStatus.accepted,
    resourceUrls: ['https://example.com/geometry_proofs.pdf'],
    teacherNote: 'Remember to cite all axioms used',
    classId: 'class_math_10a',
    teacherId: 'teacher_001',
    submissionId: 'sub_001',
  ),

  // Science homework
  HomeworkModel(
    id: 'hw_003',
    subject: 'Physics',
    title: 'Newton\'s Laws Lab Report',
    description:
        'Write a detailed lab report on the Newton\'s laws experiment.',
    assignedAt: DateTime.now().subtract(const Duration(days: 7)),
    dueAt: DateTime.now().add(const Duration(days: 1)),
    requiresSubmission: true,
    submissionType: SubmissionType.online,
    status: HomeworkStatus.done,
    resourceUrls: [
      'https://example.com/lab_instructions.pdf',
      'https://example.com/data_sheet.xlsx',
    ],
    teacherNote: 'Include graphs and error analysis',
    classId: 'class_physics_11b',
    teacherId: 'teacher_002',
  ),

  HomeworkModel(
    id: 'hw_004',
    subject: 'Chemistry',
    title: 'Periodic Table Quiz Preparation',
    description: 'Study the first 20 elements and their properties.',
    assignedAt: DateTime.now().subtract(const Duration(days: 2)),
    dueAt: DateTime.now().add(const Duration(days: 3)),
    requiresSubmission: false,
    submissionType: SubmissionType.offline,
    status: HomeworkStatus.pending,
    resourceUrls: ['https://example.com/periodic_table_guide.pdf'],
    teacherNote: 'Quiz will be held next Monday',
    classId: 'class_chemistry_11a',
    teacherId: 'teacher_003',
  ),

  // English homework
  HomeworkModel(
    id: 'hw_005',
    subject: 'English Literature',
    title: 'Shakespeare Essay',
    description: 'Write a 1000-word essay on the themes in Hamlet.',
    assignedAt: DateTime.now().subtract(const Duration(days: 10)),
    dueAt: DateTime.now().add(const Duration(days: 5)),
    requiresSubmission: true,
    submissionType: SubmissionType.online,
    status: HomeworkStatus.rejected,
    resourceUrls: [
      'https://example.com/hamlet_text.pdf',
      'https://example.com/essay_guidelines.pdf',
    ],
    teacherNote: 'Use MLA format for citations',
    classId: 'class_english_12a',
    teacherId: 'teacher_004',
  ),

  HomeworkModel(
    id: 'hw_006',
    subject: 'English Grammar',
    title: 'Sentence Structure Practice',
    description: 'Complete the grammar exercises on complex sentences.',
    assignedAt: DateTime.now().subtract(const Duration(days: 1)),
    dueAt: DateTime.now().add(const Duration(days: 4)),
    requiresSubmission: true,
    submissionType: SubmissionType.online,
    status: HomeworkStatus.pending,
    resourceUrls: ['https://example.com/grammar_exercises.pdf'],
    classId: 'class_english_10b',
    teacherId: 'teacher_004',
  ),

  // History homework
  HomeworkModel(
    id: 'hw_007',
    subject: 'World History',
    title: 'World War II Timeline',
    description: 'Create a detailed timeline of major WWII events.',
    assignedAt: DateTime.now().subtract(const Duration(days: 6)),
    dueAt: DateTime.now().add(const Duration(days: 1)),
    requiresSubmission: true,
    submissionType: SubmissionType.online,
    status: HomeworkStatus.done,
    resourceUrls: [
      'https://example.com/wwii_resources.pdf',
      'https://example.com/timeline_template.docx',
    ],
    teacherNote: 'Include both European and Pacific theaters',
    classId: 'class_history_11a',
    teacherId: 'teacher_005',
  ),

  // Biology homework
  HomeworkModel(
    id: 'hw_008',
    subject: 'Biology',
    title: 'Cell Division Diagram',
    description: 'Draw and label the stages of mitosis.',
    assignedAt: DateTime.now().subtract(const Duration(days: 4)),
    dueAt: DateTime.now().add(const Duration(days: 3)),
    requiresSubmission: true,
    submissionType: SubmissionType.offline,
    status: HomeworkStatus.pending,
    resourceUrls: ['https://example.com/mitosis_guide.pdf'],
    teacherNote: 'Use colored pencils for different phases',
    classId: 'class_biology_10a',
    teacherId: 'teacher_006',
  ),

  // Computer Science homework
  HomeworkModel(
    id: 'hw_009',
    subject: 'Computer Science',
    title: 'Python Programming Assignment',
    description: 'Write a program to calculate Fibonacci sequence.',
    assignedAt: DateTime.now().subtract(const Duration(days: 8)),
    dueAt: DateTime.now().subtract(const Duration(days: 2)),
    requiresSubmission: true,
    submissionType: SubmissionType.online,
    status: HomeworkStatus.completed,
    resourceUrls: [
      'https://example.com/python_tutorial.pdf',
      'https://example.com/fibonacci_examples.py',
    ],
    teacherNote: 'Include both recursive and iterative solutions',
    classId: 'class_cs_12a',
    teacherId: 'teacher_007',
    submissionId: 'sub_002',
  ),

  HomeworkModel(
    id: 'hw_010',
    subject: 'Computer Science',
    title: 'Database Design Project',
    description: 'Design a database schema for a library management system.',
    assignedAt: DateTime.now().subtract(const Duration(days: 12)),
    dueAt: DateTime.now().add(const Duration(days: 8)),
    requiresSubmission: true,
    submissionType: SubmissionType.online,
    status: HomeworkStatus.pending,
    resourceUrls: [
      'https://example.com/database_design_guide.pdf',
      'https://example.com/er_diagram_tool.html',
    ],
    teacherNote: 'Include ER diagram and normalization',
    classId: 'class_cs_12a',
    teacherId: 'teacher_007',
  ),

  // Additional homework entries using faker for variety
  ...List.generate(40, (index) {
    final faker = Faker();
    final subjects = [
      'Mathematics',
      'Physics',
      'Chemistry',
      'Biology',
      'English Literature',
      'History',
      'Geography',
      'Computer Science',
      'Art',
      'Music',
      'Physical Education',
      'Economics',
      'Psychology',
      'Philosophy',
    ];

    final submissionTypes = [SubmissionType.online, SubmissionType.offline];
    final statuses = [
      HomeworkStatus.pending,
      HomeworkStatus.done,
      HomeworkStatus.submitted,
      HomeworkStatus.completed,
    ];

    final subject = subjects[index % subjects.length];
    final assignedDaysAgo = faker.randomGenerator.integer(30, min: 1);
    final dueDaysFromNow = faker.randomGenerator.integer(14, min: -7);
    final status = statuses[index % statuses.length];
    final submissionType = submissionTypes[index % submissionTypes.length];

    return HomeworkModel(
      id: 'hw_${(index + 11).toString().padLeft(3, '0')}',
      subject: subject,
      title: faker.lorem.sentence().replaceAll('.', ''),
      description: faker.randomGenerator.boolean()
          ? faker.lorem.sentences(2).join(' ')
          : null,
      assignedAt: DateTime.now().subtract(Duration(days: assignedDaysAgo)),
      dueAt: faker.randomGenerator.boolean()
          ? DateTime.now().add(Duration(days: dueDaysFromNow))
          : null,
      requiresSubmission: faker.randomGenerator.boolean(),
      submissionType: submissionType,
      status: status,
      resourceUrls: List.generate(
        faker.randomGenerator.integer(4, min: 0),
        (i) => 'https://example.com/${faker.lorem.word()}_${i + 1}.pdf',
      ),
      teacherNote: faker.randomGenerator.boolean()
          ? faker.lorem.sentence()
          : null,
      classId:
          'class_${subject.toLowerCase().replaceAll(' ', '_')}_${faker.randomGenerator.integer(3, min: 1)}${String.fromCharCode(97 + faker.randomGenerator.integer(3))}',
      teacherId:
          'teacher_${faker.randomGenerator.integer(20, min: 1).toString().padLeft(3, '0')}',
      submissionId:
          (status == HomeworkStatus.submitted ||
              status == HomeworkStatus.completed)
          ? 'sub_${faker.randomGenerator.integer(100, min: 1).toString().padLeft(3, '0')}'
          : null,
    );
  }),
];

/// Mock data for homework submissions
final List<HomeworkSubmissionModel> mockHomeworkSubmissions = [
  HomeworkSubmissionModel(
    homeworkId: 'hw_002',
    userId: 'student_001',
    fileUrls: [
      'https://example.com/submissions/geometry_proof_solution.pdf',
      'https://example.com/submissions/geometry_diagrams.png',
    ],
    studentNote:
        'I had some difficulty with theorem 3, but I think I got it right.',
    submittedAt: DateTime.now().subtract(const Duration(hours: 6)),
    teacherRemark:
        'Good work! Your approach to theorem 3 is correct. Well done.',
    reviewedAt: DateTime.now().subtract(const Duration(hours: 2)),
  ),

  HomeworkSubmissionModel(
    homeworkId: 'hw_009',
    userId: 'student_002',
    fileUrls: [
      'https://example.com/submissions/fibonacci_recursive.py',
      'https://example.com/submissions/fibonacci_iterative.py',
      'https://example.com/submissions/performance_comparison.txt',
    ],
    studentNote:
        'Included both solutions as requested. The iterative version is much faster for large numbers.',
    submittedAt: DateTime.now().subtract(const Duration(days: 3, hours: 2)),
    teacherRemark:
        'Excellent analysis! Your performance comparison is very insightful.',
    reviewedAt: DateTime.now().subtract(const Duration(days: 2, hours: 5)),
  ),

  // Example of rejected submission
  HomeworkSubmissionModel(
    homeworkId: 'hw_005',
    userId: 'student_003',
    fileUrls: ['https://example.com/submissions/essay_draft.docx'],
    studentNote: 'Here is my essay on Hamlet themes.',
    submittedAt: DateTime.now().subtract(const Duration(days: 1, hours: 8)),
    teacherRemark:
        'Please revise your essay. The analysis needs more depth and proper citations are missing.',
    reviewedAt: DateTime.now().subtract(const Duration(hours: 12)),
  ),

  // Example of pending submission (no teacher review yet)
  HomeworkSubmissionModel(
    homeworkId: 'hw_001',
    userId: 'student_004',
    fileUrls: [
      'https://example.com/submissions/algebra_solutions.pdf',
      'https://example.com/submissions/work_shown.jpg',
    ],
    studentNote: 'All problems solved with detailed work shown.',
    submittedAt: DateTime.now().subtract(const Duration(hours: 3)),
    // No teacher remark or review date yet
  ),

  // Generate additional mock submissions
  ...List.generate(25, (index) {
    final faker = Faker();
    final submittedHomeworkIds = mockHomeworkList
        .where(
          (hw) =>
              hw.status == HomeworkStatus.submitted ||
              hw.status == HomeworkStatus.completed,
        )
        .map((hw) => hw.id)
        .toList();

    if (submittedHomeworkIds.isEmpty) return null;

    final homeworkId =
        submittedHomeworkIds[index % submittedHomeworkIds.length];
    final fileCount = faker.randomGenerator.integer(4, min: 1);

    return HomeworkSubmissionModel(
      homeworkId: homeworkId,
      userId: 'student_${(index + 3).toString().padLeft(3, '0')}',
      fileUrls: List.generate(fileCount, (i) {
        final extensions = ['pdf', 'docx', 'jpg', 'png', 'txt', 'py', 'java'];
        final ext =
            extensions[faker.randomGenerator.integer(extensions.length)];
        return 'https://example.com/submissions/${faker.lorem.word()}_${i + 1}.$ext';
      }),
      studentNote: faker.randomGenerator.boolean()
          ? faker.lorem.sentence()
          : null,
      submittedAt: DateTime.now().subtract(
        Duration(
          days: faker.randomGenerator.integer(10, min: 1),
          hours: faker.randomGenerator.integer(24),
          minutes: faker.randomGenerator.integer(60),
        ),
      ),
    );
  }).where((submission) => submission != null).cast<HomeworkSubmissionModel>(),
];
