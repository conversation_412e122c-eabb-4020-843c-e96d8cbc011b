import 'package:go_router/go_router.dart';
import 'package:scholara_student/features/dashboard/screens/dashboard_screen.dart';
import 'package:scholara_student/features/homework/screens/homework_list_screen.dart';
import 'package:scholara_student/features/homework/screens/homework_detail_screen.dart';
import 'app_routes.dart';

final GoRouter appRouter = GoRouter(
  initialLocation: AppRoutes.home,
  routes: [
    GoRoute(
      path: AppRoutes.home,
      name: RouteNames.home,
      builder: (context, state) => const DashboardScreen(),
    ),
    GoRoute(
      path: AppRoutes.homeworkList,
      name: RouteNames.homeworkList,
      builder: (context, state) => const HomeworkListScreen(),
    ),
    GoRoute(
      path: AppRoutes.homeworkDetail,
      name: RouteNames.homeworkDetail,
      builder: (context, state) {
        final id = state.pathParameters['id']!;
        return HomeworkDetailScreen(homeworkId: id);
      },
    ),
  ],
);



// context.pushNamed() -->	Navigate forward (adds to stack)
// context.goNamed() -->	Navigate and clear previous stack
// pathParameters	--> For dynamic routes like /homework/:id
// initialLocation -->	Define default route on app start
// GoRouterRefreshStream -->	Auto-redirect when auth state changes (advanced)

