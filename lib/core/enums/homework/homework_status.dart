import 'package:flutter/material.dart';

enum HomeworkStatus { pending, done, submitted, accepted, rejected }

/// Extension to provide additional functionality for HomeworkStatus
extension HomeworkStatusExtension on HomeworkStatus {
  /// Returns a human-readable label for the homework status
  String get label {
    switch (this) {
      case HomeworkStatus.pending:
        return 'Pending';
      case HomeworkStatus.done:
        return 'Done';
      case HomeworkStatus.submitted:
        return 'Submitted';
      case HomeworkStatus.accepted:
        return 'Accepted';
      case HomeworkStatus.rejected:
        return 'Rejected';
    }
  }

  /// Returns the color associated with the homework status
  Color get color {
    switch (this) {
      case HomeworkStatus.pending:
        return Colors.orange;
      case HomeworkStatus.done:
        return Colors.blue;
      case HomeworkStatus.submitted:
        return Colors.purple;
      case HomeworkStatus.accepted:
        return Colors.green;
      case HomeworkStatus.rejected:
        return Colors.red;
    }
  }

  /// Returns whether the homework submission can be edited (for online submissions)
  bool get canEditSubmission {
    switch (this) {
      case HomeworkStatus.pending:
      case HomeworkStatus.submitted: // Can still edit submitted submissions
      case HomeworkStatus.rejected:
        return true;
      case HomeworkStatus.done: // Not applicable for online submissions
      case HomeworkStatus.accepted: // Cannot edit accepted submissions
        return false;
    }
  }

  /// Returns whether the homework can be submitted (for online submissions)
  bool get canSubmit {
    switch (this) {
      case HomeworkStatus.pending: // Can submit from pending
        return true;
      case HomeworkStatus.done: // Not applicable for online submissions
      case HomeworkStatus.submitted: // Already submitted
      case HomeworkStatus.accepted: // Already accepted
      case HomeworkStatus.rejected: // Need to edit first, then submit
        return false;
    }
  }

  /// Returns whether homework can be marked as done (for offline/no submission)
  bool get canMarkDone {
    switch (this) {
      case HomeworkStatus.pending:
        return true;
      case HomeworkStatus.done:
      case HomeworkStatus.submitted:
      case HomeworkStatus.accepted:
      case HomeworkStatus.rejected:
        return false;
    }
  }

  /// Returns whether homework can be marked as undone
  bool get canMarkUndone {
    switch (this) {
      case HomeworkStatus.done:
        return true;
      case HomeworkStatus.pending:
      case HomeworkStatus.submitted:
      case HomeworkStatus.accepted:
      case HomeworkStatus.rejected:
        return false;
    }
  }
}
