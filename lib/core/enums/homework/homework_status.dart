import 'package:flutter/material.dart';

enum HomeworkStatus {
  pending,
  done,
  submitted,
  completed,
}

/// Extension to provide additional functionality for HomeworkStatus
extension HomeworkStatusExtension on HomeworkStatus {
  /// Returns a human-readable label for the homework status
  String get label {
    switch (this) {
      case HomeworkStatus.pending:
        return 'Pending';
      case HomeworkStatus.done:
        return 'Done';
      case HomeworkStatus.submitted:
        return 'Submitted';
      case HomeworkStatus.completed:
        return 'Completed';
    }
  }

  /// Returns the color associated with the homework status
  Color get color {
    switch (this) {
      case HomeworkStatus.pending:
        return Colors.orange;
      case HomeworkStatus.done:
        return Colors.blue;
      case HomeworkStatus.submitted:
        return Colors.purple;
      case HomeworkStatus.completed:
        return Colors.green;
    }
  }

  /// Returns whether the homework can be edited
  bool get canEdit {
    switch (this) {
      case HomeworkStatus.pending:
      case HomeworkStatus.done:
        return true;
      case HomeworkStatus.submitted:
      case HomeworkStatus.completed:
        return false;
    }
  }

  /// Returns whether the homework can be submitted
  bool get canSubmit {
    switch (this) {
      case HomeworkStatus.pending:
      case HomeworkStatus.done:
        return true;
      case HomeworkStatus.submitted:
      case HomeworkStatus.completed:
        return false;
    }
  }
}
